# Door Sensor Integration Summary

## Overview
Successfully integrated door sensor data into the existing temperature visualization in `myutils/IPQ-THSensors.py`. The door sensor data is now displayed as an overlay on the temperature plot using a secondary y-axis.

## Key Changes Made

### 1. New Function: `retrieve_door_sensor_data()`
- **Location**: Added before `retrieve_sensor_data_for_measurement_type()`
- **Purpose**: Retrieves door sensor data from InfluxDB with proper time offset handling
- **Data Source**: 
  - Bucket: `IPQ_DoorData`
  - Device: `DoorSensor 1`
  - Measurement: `device_frmpayload_data_door_open_status`
- **Returns**: DataFrame with time index and binary values (0=closed, 1=open)

### 2. Enhanced Main Plotting Function
- **Function**: `plot_multiple_sensors_temperature_and_humidity()`
- **New Parameter**: `plot_door_sensor=True` (optional)
- **Updated Docstring**: Documents the new door sensor functionality

### 3. Door Sensor Visualization Features
- **Secondary Y-Axis**: Door sensor data overlays on temperature plot using right y-axis
- **Visual Style**: 
  - Red step function line with transparency
  - Red filled areas for visual distinction
  - Clear labels: "Closed" and "Open"
- **Legend Integration**: Door sensor appears in the combined legend with temperature sensors

### 4. Data Handling
- **Time Synchronization**: Same 2-hour offset correction as temperature data
- **Error Handling**: Graceful fallback if door sensor data is unavailable
- **Data Storage**: Door sensor data included in returned data dictionary as `"door_sensor"`

## Usage Examples

### Basic Usage (with door sensor)
```python
data = plot_multiple_sensors_temperature_and_humidity(
    sensor_ids=['IPQ_TH14', 'IPQ_TH11'],
    sensor_labels=['Inside Sonata', 'Outside Sonata'],
    plot_temperature=True,
    plot_humidity=True,
    plot_door_sensor=True,  # Enable door sensor overlay
    start_datetime='2025-08-18T00:00:00Z'
)
```

### Disable Door Sensor
```python
data = plot_multiple_sensors_temperature_and_humidity(
    sensor_ids=['IPQ_TH14', 'IPQ_TH11'],
    plot_door_sensor=False,  # Disable door sensor overlay
    # ... other parameters
)
```

## Visual Layout
- **Temperature Plot**: Primary plot with temperature sensors (left y-axis)
- **Door Sensor Overlay**: Secondary y-axis (right side) showing open/closed states
- **Humidity Plot**: Separate subplot below (if enabled)
- **Time Axis**: Shared across all plots with day/night overlays

## Data Structure
The returned data dictionary now includes:
```python
{
    'IPQ_TH14_temperature': DataFrame,
    'IPQ_TH11_temperature': DataFrame,
    'IPQ_TH14_humidity': DataFrame,     # if humidity enabled
    'IPQ_TH11_humidity': DataFrame,     # if humidity enabled
    'door_sensor': DataFrame            # if door sensor enabled and data available
}
```

## Error Handling
- Graceful handling when door sensor data is unavailable
- Warning messages for missing data
- Continues plotting temperature/humidity even if door sensor fails

## Testing
Created `test_door_sensor_integration.py` to verify:
- Door sensor data retrieval
- Proper overlay visualization
- Backward compatibility when door sensor is disabled
- Error handling for missing data

## Installation Notes
- Door sensor was installed at 13:27 on 2025-08-18
- Data is available from that time forward
- Uses the same InfluxDB connection as temperature sensors but different bucket (`IPQ_DoorData`)
