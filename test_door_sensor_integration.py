#!/usr/bin/env python3
"""
Test script to verify door sensor integration with temperature plotting.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'myutils'))

from datetime import datetime, timezone
from IPQ_THSensors import plot_multiple_sensors_temperature_and_humidity

def test_door_sensor_integration():
    """Test the door sensor integration with temperature plotting."""
    
    print("Testing door sensor integration with temperature plotting...")
    
    # Test parameters
    sensor_ids = ['IPQ_TH14', 'IPQ_TH11']
    sensor_labels = ['Inside Sonata', 'Outside Sonata']
    
    # Use a shorter time range for testing (last 6 hours)
    start_datetime = '2025-08-18T06:00:00Z'
    stop_datetime = datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
    
    print(f"Sensors: {sensor_ids}")
    print(f"Time range: {start_datetime} to {stop_datetime}")
    
    try:
        # Test with door sensor enabled
        print("\n--- Testing with door sensor enabled ---")
        data = plot_multiple_sensors_temperature_and_humidity(
            sensor_ids=sensor_ids,
            start_datetime=start_datetime,
            stop_datetime=stop_datetime,
            plot_temperature=True,
            plot_humidity=False,  # Only temperature for this test
            plot_door_sensor=True,
            time_interval=1,  # 1 hour intervals for better resolution
            figsize=(15, 8),
            sensor_labels=sensor_labels,
            save_plot=True,
            filename="test_with_door_sensor.png"
        )
        
        print(f"Retrieved data keys: {list(data.keys())}")
        
        # Test without door sensor
        print("\n--- Testing without door sensor ---")
        data_no_door = plot_multiple_sensors_temperature_and_humidity(
            sensor_ids=sensor_ids,
            start_datetime=start_datetime,
            stop_datetime=stop_datetime,
            plot_temperature=True,
            plot_humidity=False,
            plot_door_sensor=False,
            time_interval=1,
            figsize=(15, 8),
            sensor_labels=sensor_labels,
            save_plot=True,
            filename="test_without_door_sensor.png"
        )
        
        print(f"Retrieved data keys (no door): {list(data_no_door.keys())}")
        
        print("\n✅ Door sensor integration test completed successfully!")
        
        # Check if door sensor data was retrieved
        if "door_sensor" in data:
            door_data = data["door_sensor"]
            print(f"Door sensor data points: {len(door_data)}")
            print(f"Door sensor value range: {door_data['value'].min()} to {door_data['value'].max()}")
        else:
            print("⚠️  No door sensor data was retrieved")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_door_sensor_integration()
