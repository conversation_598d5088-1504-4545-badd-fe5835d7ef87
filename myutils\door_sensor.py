from influxdb_client import InfluxDBClient
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime

# Connection Data
url = "http://*************:8086"
token = "4SPXQ9_jeeobEkc3hswm2Kdd93C9a3fmchDRIqePg8IzMNem7AQhoo7BCiPFht4cDszS5mPrcjMrxcL5YfPf3g=="
org = "IPQ"
bucket = "IPQ_SensorData"

def query_door_sensor_data(time_range="-24h"):
    """
    Query door sensor data from InfluxDB

    Args:
        time_range (str): Time range for the query (e.g., "-24h", "-1d", "-1w")

    Returns:
        pandas.DataFrame: DataFrame containing door sensor data
    """
    # Flux Query
    query = f'''
    from(bucket: "IPQ_DoorData")
      |> range(start: {time_range})
      |> filter(fn: (r) => r["device_name"] == "DoorSensor 1")
      |> filter(fn: (r) => r["_measurement"] == "device_frmpayload_data_door_open_status")
      |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
      |> yield(name: "mean")
    '''

    # InfluxDB Client
    client = InfluxDBClient(url=url, token=token, org=org)
    query_api = client.query_api()

    # Query and return data
    df = query_api.query_data_frame(query=query, org=org)
    client.close()
    return df

def prepare_door_state_data(df):
    """
    Prepare door sensor data for visualization

    Args:
        df (pandas.DataFrame): Raw door sensor data

    Returns:
        tuple: (timestamps, door_states) ready for plotting
    """
    if df.empty:
        print("No data available for the specified time range.")
        return None, None

    # Convert timestamp to datetime and sort by time
    df['_time'] = pd.to_datetime(df['_time'])
    df = df.sort_values('_time')

    # Extract timestamps and door states
    timestamps = df['_time'].values
    door_states = df['value'].values

    return timestamps, door_states

def plot_door_state_timeline(timestamps, door_states, save_plot=True, show_plot=True):
    """
    Create a timeline visualization of door state (open/closed) over time

    Args:
        timestamps (array): Array of timestamp values
        door_states (array): Array of door state values (0=closed, 1=open)
        save_plot (bool): Whether to save the plot as an image file
        show_plot (bool): Whether to display the plot
    """
    if timestamps is None or door_states is None:
        print("No data to plot.")
        return

    # Create figure and axis
    fig, ax = plt.subplots(figsize=(14, 6))

    # Create step plot for door states
    for i, t in enumerate(timestamps):
        print(f"{t}: {door_states[i]}")
    ax.step(timestamps, door_states, where='post', linewidth=2,
            color='steelblue', label='Door State')

    # Fill areas for visual distinction
    ax.fill_between(timestamps, door_states, step='post', alpha=0.3,
                    color='lightgreen', label='Door Open')
    ax.fill_between(timestamps, 0, door_states, step='post', alpha=0.3,
                    color='lightcoral', label='Door Closed')

    # Customize the plot
    ax.set_ylim(-0.1, 1.1)
    ax.set_yticks([0, 1])
    ax.set_yticklabels(['Closed', 'Open'])
    ax.set_ylabel('Door State', fontsize=12, fontweight='bold')
    ax.set_xlabel('Time', fontsize=12, fontweight='bold')
    ax.set_title('Door Sensor State Over Time', fontsize=14, fontweight='bold')

    # Format x-axis for better readability
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M\n%m/%d'))
    ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
    plt.xticks(rotation=0)

    # Add grid for better readability
    ax.grid(True, alpha=0.3, linestyle='--')

    # Add legend
    ax.legend(loc='upper right')

    # Adjust layout
    plt.tight_layout()

    # Save plot if requested
    if save_plot:
        timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'door_sensor_timeline_{timestamp_str}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"Plot saved as: {filename}")

    # Show plot if requested
    if show_plot:
        plt.show()

    return fig, ax

# Main execution
if __name__ == "__main__":
    # Query door sensor data
    print("Querying door sensor data...")
    df = query_door_sensor_data(time_range="-24h")

    # Prepare data for visualization
    timestamps, door_states = prepare_door_state_data(df)

    if timestamps is not None:
        print(f"Retrieved {len(timestamps)} data points")

        # Create visualization
        print("Creating door state timeline visualization...")
        plot_door_state_timeline(timestamps, door_states)

        # Export to CSV (keeping original functionality)
        df.to_csv("export.csv", index=False)
        print("Export worked! Saved as 'export.csv'")
    else:
        print("No data available to visualize.")
